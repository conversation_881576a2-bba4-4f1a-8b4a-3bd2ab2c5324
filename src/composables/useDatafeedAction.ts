import { storeToRefs } from "pinia"
import { ref, watch, nextTick } from "vue"
import { commonStore } from "~/stores/commonStore"
import { getKlinesApi } from '~/api/order'

export class ResolutionManager {
  static readonly RESOLUTION_MAP: any = {
    1: '1m',
    5: '5m',
    15: '15m',
    30: '30m',
    60: '1h',
    120: '2h',
    240: '4h',
    360: '6h',
    480: '8h',
    720: '12h',
    '1D': '1d',
    '1W': '1w',
    '1M': '1M'
  }

  static readonly RESOLUTION_RE_MAP: any = {
    'line': 1,
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '2h': 120,
    '4h': 240,
    '6h': 360,
    '8h': 480,
    '12h': 720,
    '1d': '1D',
    '1w': '1W',
    '1M': '1M'
  }

  static isMonthly(resolution: string): boolean {
    return resolution === '1M'
  }

  static getInterval(resolution: string): string {
    return this.RESOLUTION_MAP[resolution] || resolution
  }

  static getResolution(interval: string): string {
    return this.RESOLUTION_RE_MAP[interval] || interval
  }
}

interface DataParams {
  symbol: string
  resolution: string
  from: number
  to: number
  firstDataRequest: boolean
  countBack: number
}

interface DataResult {
  data: any[]
  noData: boolean
}

abstract class DataStrategy {
  protected dataCache: Map<string, any>
  protected CACHE_DURATION: number

  constructor(dataCache: Map<string, any>, cacheDuration: number) {
    this.dataCache = dataCache
    this.CACHE_DURATION = cacheDuration
  }

  abstract async getData(params: DataParams, onHistoryCallback: any, onErrorCallback: any): Promise<boolean>
  abstract getCacheKey(params: DataParams): string
  abstract getCacheTimeout(): number
}

class StandardDataStrategy extends DataStrategy {
  getCacheKey(params: DataParams): string {
    return `${params.symbol}-${params.resolution}-${params.firstDataRequest ? 'first' : params.from}`
  }

  getCacheTimeout(): number {
    return this.CACHE_DURATION
  }

  async getData(params: DataParams, onHistoryCallback: any, onErrorCallback: any): Promise<boolean> {
    return false
  }
}

class MonthlyDataStrategy extends DataStrategy {
  private klineList: any
  private klineTicker: any
  private preObj: any

  constructor(dataCache: Map<string, any>, cacheDuration: number, klineList: any, klineTicker: any, preObj: any) {
    super(dataCache, cacheDuration)
    this.klineList = klineList
    this.klineTicker = klineTicker
    this.preObj = preObj
  }

  getCacheKey(params: DataParams): string {
    return `${params.symbol}-1M-first`
  }

  getCacheTimeout(): number {
    return 60 * 1000
  }

  async getData(params: DataParams, onHistoryCallback: any, onErrorCallback: any): Promise<boolean> {
    if (!params.firstDataRequest || !ResolutionManager.isMonthly(params.resolution)) {
      return false
    }

    // 首先检查store中是否有当前币种的1M数据
    if (this.klineList.value.length && this.klineTicker.value.currentPeriod === '1M' &&
        this.klineTicker.value.currentPair === params.symbol) {
      this.preObj.value = this.klineList.value[0]
      onHistoryCallback(this.klineList.value, {noData: this.klineList.value.length === 0})
      return true
    }

    // 检查缓存
    const cacheKey = this.getCacheKey(params)
    const cachedData = this.dataCache.get(cacheKey)

    if (cachedData && (Date.now() - cachedData.timestamp < this.getCacheTimeout())) {
      this.klineList.value = cachedData.data
      if (cachedData.data.length > 0) {
        this.klineTicker.value = {
          ...cachedData.data[cachedData.data.length - 1],
          currentPair: params.symbol,
          currentPeriod: '1M'
        }
        this.preObj.value = cachedData.data[0]
      }
      onHistoryCallback(cachedData.data, {noData: cachedData.data.length === 0})
      return true
    }

    // 如果没有store数据也没有缓存，返回false让常规逻辑处理
    return false
  }

}

class DataStrategyFactory {
  static createStrategy(resolution: string, dataCache: Map<string, any>, cacheDuration: number, klineList?: any, klineTicker?: any, preObj?: any): DataStrategy {
    if (ResolutionManager.isMonthly(resolution)) {
      return new MonthlyDataStrategy(dataCache, cacheDuration, klineList, klineTicker, preObj)
    }
    return new StandardDataStrategy(dataCache, cacheDuration)
  }
}

export default function useDatafeedAction(info) {
  const dataCache = new Map()
  const CACHE_DURATION = 2 * 60 * 1000
  const pairInfo = info
  const store = commonStore()
  const interval = ref('')
  const pair = ref('')
  const preObj = ref({})
  const { klineList, klineTicker, ticker } = storeToRefs(store)

  // 检测是否为浏览器刷新后的初始化
  const isPageRefresh = ref(!klineList.value || klineList.value.length === 0)

  let debounceTimer = null
  const pendingRequests = new Map()
  const abortControllers = new Map()
  const activeBarsRequests = new Map()
  const subMap: any = {}
  let rafId: number | null = null

  function formatSymbol(symbol: string) {
    return symbol.toUpperCase()
  }

  const safeNumber = (value: any, fallback: number = 0): number => {
    const num = Number(value)
    return isNaN(num) || !isFinite(num) ? fallback : Math.abs(num)
  }
  let key = ''
  async function handleMonthlyData(symbolInfo: any, resolution: any, periodParams: any, onHistoryCallback: any, onErrorCallback: any) {
    const strategy = DataStrategyFactory.createStrategy(resolution, dataCache, CACHE_DURATION, klineList, klineTicker, preObj)

    const params: DataParams = {
      symbol: symbolInfo.fullName,
      resolution,
      from: periodParams.from,
      to: periodParams.to,
      firstDataRequest: periodParams.firstDataRequest,
      countBack: periodParams.countBack
    }

    const result = await strategy.getData(params, onHistoryCallback, onErrorCallback)
    return result
  }

  async function getBars(
    symbolInfo: any,
    resolution: any,
    periodParams: any,
    onHistoryCallback: any,
    onErrorCallback: any) {
    const currentSymbol = symbolInfo.fullName
    const { from, to, firstDataRequest, countBack } = periodParams;

    // 创建请求唯一标识
    const requestId = `${currentSymbol}-${resolution}-${firstDataRequest ? 'first' : 'history'}`

    // 如果相同的请求正在进行，直接返回
    if (activeBarsRequests.has(requestId)) {
      return
    }

    // 标记请求开始
    activeBarsRequests.set(requestId, true)

    // 对于1M数据，使用特殊的请求管理策略
    if (ResolutionManager.isMonthly(resolution)) {
      // 对于1M数据，清理同一交易对的旧请求，避免并发冲突
      const monthlyPattern = `${currentSymbol}-1M-`
      const monthlyRequestsToDelete = []
      for (const [activeRequestId] of activeBarsRequests.entries()) {
        if (activeRequestId.startsWith(monthlyPattern) && activeRequestId !== requestId) {
          monthlyRequestsToDelete.push(activeRequestId)
        }
      }
      monthlyRequestsToDelete.forEach(id => {
        activeBarsRequests.delete(id)
      })
      
      // 1M数据切换时清理不相关的store数据
      if (klineTicker.value.currentPeriod && klineTicker.value.currentPeriod !== '1M') {
        // 只清理不同周期的数据，保留同交易对的1M数据
        if (klineTicker.value.currentPair !== currentSymbol) {
          klineList.value = []
          klineTicker.value = {}
        }
      }
    } else {
      // 检查并清理旧的请求，特别是非1M周期的请求
      const sameTypeRequestPattern = `${currentSymbol}-${resolution}-${firstDataRequest ? 'first' : 'history'}`
      const requestsToDelete = []
      for (const [activeRequestId] of activeBarsRequests.entries()) {
        if (activeRequestId.startsWith(sameTypeRequestPattern) && !activeRequestId.includes(timestamp.toString())) {
          requestsToDelete.push(activeRequestId)
        }
      }
      // 在遍历外删除，避免在遍历时修改Map
      requestsToDelete.forEach(id => activeBarsRequests.delete(id))
    }

    activeBarsRequests.set(requestId, true)

    // 立即更新pair和interval，确保后续检查能通过
    const oldInterval = interval.value
    const newInterval = ResolutionManager.getInterval(resolution)
    
    pair.value = currentSymbol
    interval.value = newInterval
    key = `${currentSymbol}-${resolution}`
    
    // 如果是从其他周期切换到1M，需要特殊处理
    if (newInterval === '1M' && oldInterval !== '1M' && oldInterval !== '') {
      // 清理不同周期的lastCompleteBar
      Object.keys(lastCompleteBar.value).forEach(barKey => {
        if (!barKey.includes('1M') && barKey.includes(currentSymbol)) {
          delete lastCompleteBar.value[barKey]
        }
      })
      
      // 标记月度订阅需要更新
      if (monthlySubscriptionCache.pair === currentSymbol) {
        monthlySubscriptionCache.needsUpdate = true
      }
      
      // 如果已有klineTicker数据且1M周期，立即触发更新
      if (klineTicker.value && klineTicker.value.currentPeriod === '1M' && 
          klineTicker.value.currentPair === currentSymbol) {
        // 强制触发响应式更新
        nextTick(() => {
          klineTicker.value = { ...klineTicker.value }
        })
      }
    }

    if (debounceTimer) {
      clearTimeout(debounceTimer)
      debounceTimer = null
    }

    // 保守的防抖优化：轻微减少延迟但保持稳定性
    const debounceDelay = firstDataRequest ?
      (ResolutionManager.isMonthly(resolution) ? 40 : 30) :  // 轻微减少首次请求延迟
      (ResolutionManager.isMonthly(resolution) ? 100 : 80)   // 保持相对保守的历史请求延迟

    const executeRequest = async () => {
      try {
        if (resolution === '1M') {
          const handled = await handleMonthlyData(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback)
          if (handled) {
            return
          }
        }

        await fetchHistoricalData(symbolInfo.fullName, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback);
      } finally {
        activeBarsRequests.delete(requestId)
      }
    }

    // 所有请求都使用防抖
    debounceTimer = setTimeout(executeRequest, debounceDelay)
  }
  const forceRefresh = ref(false)

  const clearCache = (selective = false, currentSymbol = '', currentResolution = '') => {

    if (selective && currentSymbol && currentResolution) {
      // 选择性清理：只取消不相关的请求，保留当前请求
      const currentRequestPrefix = `${currentSymbol}-${ResolutionManager.getResolution(currentResolution)}`

      // 简化的清理策略：如果请求太多（超过2个），直接清空
      // 这样可以避免请求堆积导致的阻塞
      if (activeBarsRequests.size > 2) {
        activeBarsRequests.clear()
      } else {
        // 只清理其他symbol的请求
        const requestsToDelete = []
        for (const [requestId] of activeBarsRequests.entries()) {
          if (!requestId.includes(currentSymbol)) {
            requestsToDelete.push(requestId)
          }
        }
        requestsToDelete.forEach(id => {
          activeBarsRequests.delete(id)
        })
      }

      // 取消不相关的pending请求和abortControllers
      const controllersToDelete = []
      const pendingToDelete = []

      for (const [key, controller] of abortControllers.entries()) {
        // 使用更宽松的匹配，避免过度清理
        if (!key.includes(currentSymbol) || !key.includes(currentResolution)) {
          controller.abort()
          controllersToDelete.push(key)
        }
      }

      for (const [key] of pendingRequests.entries()) {
        if (!key.includes(currentSymbol) || !key.includes(currentResolution)) {
          pendingToDelete.push(key)
        }
      }

      controllersToDelete.forEach(key => abortControllers.delete(key))
      pendingToDelete.forEach(key => pendingRequests.delete(key))
    } else {
      // 完全清理时才清除所有请求
      activeBarsRequests.clear()

      abortControllers.forEach(controller => {
        controller.abort()
      })
      abortControllers.clear()
      pendingRequests.clear()
    }

    if (selective && currentSymbol && currentResolution) {
      // 选择性清理：保留当前交易对的相关缓存
      const currentCacheKey = `${currentSymbol}-${currentResolution}-first`
      const cacheKeysToKeep = new Set([currentCacheKey])

      // 对于1M数据，额外保留相关缓存
      if (ResolutionManager.isMonthly(currentResolution)) {
        const monthlyKey = `${currentSymbol}-1M-first`
        cacheKeysToKeep.add(monthlyKey)
      }

      // 清理旧的缓存，但保留当前需要的
      let deletedCount = 0
      for (const [key] of dataCache.entries()) {
        if (!cacheKeysToKeep.has(key)) {
          dataCache.delete(key)
          deletedCount++
        }
      }

      // 清理不相关的lastCompleteBar
      const currentBarKey = `${currentSymbol}_#_${ResolutionManager.getInterval(currentResolution)}`
      const keysToKeep = [currentBarKey]

      // 对于1M数据，也保留月度bar
      if (ResolutionManager.isMonthly(currentResolution)) {
        keysToKeep.push(`${currentSymbol}_#_1M`)
      }

      Object.keys(lastCompleteBar.value).forEach(key => {
        if (!keysToKeep.includes(key)) {
          delete lastCompleteBar.value[key]
        }
      })
    } else {
      // 完全清理（原有逻辑）
      dataCache.clear()
      lastCompleteBar.value = {}
      klineList.value = []
      klineTicker.value = {}
      preObj.value = {}
    }

    // 清理订阅映射
    if (!selective) {
      Object.keys(subMap).forEach(key => {
        delete subMap[key]
      })

      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
    }

    if (rafId) {
      cancelAnimationFrame(rafId)
      rafId = null
    }

    // 清理防抖定时器
    if (debounceTimer) {
      clearTimeout(debounceTimer)
      debounceTimer = null
    }

    // 根据清理类型决定是否取消所有请求
    if (!selective) {
      // 完全清理时才取消所有pending的请求
      abortControllers.forEach(controller => {
        controller.abort()
      })
      abortControllers.clear()
      pendingRequests.clear()
      activeBarsRequests.clear()
    }
  }

  const setForceRefresh = (force: boolean, currentSymbol = '', currentResolution = '') => {
    forceRefresh.value = force
    if (force) {
      // 使用选择性清理，避免清除当前需要的数据
      clearCache(false, currentSymbol, currentResolution)
    }
  }

  // 添加数据预加载函数
  const preloadData = async (symbol: string, resolution: string) => {
    const cacheKey = `${symbol}-${resolution}-first`
    if (dataCache.has(cacheKey)) {
      return true // 数据已存在
    }

    try {
      // 预加载数据但不直接使用，只存储到缓存
      const { data } = await getKlinesApi({
        symbol: formatSymbol(symbol),
        market: formatSymbol(symbol).includes('_SWAP') ? 'lpc' : 'spot',
        time_frame: ResolutionManager.getInterval(resolution),
        before: Date.now(),
        limit: 300,
        origin: 1,
      })

      if (data && data.e) {
        const formattedData = data.e.map(item => ({
          time: Number(item[0]),
          open: Math.abs(Number(item[1])),
          high: Math.abs(Number(item[2])),
          low: Math.abs(Number(item[3])),
          close: Math.abs(Number(item[4])),
          volume: Math.abs(Number(item[5]))
        }))

        dataCache.set(cacheKey, {
          data: formattedData,
          timestamp: Date.now()
        })

        return true
      }
    } catch (error) {
    }

    return false
  }


  const checkCacheValid = (symbol: string, resolution: string): boolean => {
    const cacheKey = `${symbol}-${resolution}-first`
    const cachedData = dataCache.get(cacheKey)

    if (!cachedData) return false

    const now = Date.now()
    const isMonthly = ResolutionManager.isMonthly(resolution)
    const cacheTimeout = isMonthly ? 30 * 1000 : 60 * 1000

    const isVeryFresh = (now - cachedData.timestamp) < cacheTimeout

    return isVeryFresh && cachedData.data && cachedData.data.length > 0
  }

  const clearCacheWithCheck = (symbol?: string, resolution?: string): boolean => {
    if (symbol && resolution) {
      const cacheKey = `${symbol}-${resolution}-first`
      const cachedData = dataCache.get(cacheKey)

      if (cachedData && (Date.now() - cachedData.timestamp < 5000)) {
        return false
      }
    }

    clearCache()
    return true
  }
  async function fetchHistoricalData(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any) {
    const cacheKey = `${symbol}-${resolution}-${firstDataRequest ? 'first' : from}`
    const timestamp = Date.now()
    const requestKey = `${symbol}-${resolution}-${from}-${to}-${firstDataRequest}-${timestamp}`

    // 优化：取消相同类型的旧请求，允许新请求进行
    const sameTypePattern = `${symbol}-${resolution}-${from}-${to}-${firstDataRequest}`
    for (const [key, controller] of abortControllers.entries()) {
      if (key.startsWith(sameTypePattern) && key !== requestKey) {
        controller.abort()
        abortControllers.delete(key)
        pendingRequests.delete(key)
      }
    }

    if (pendingRequests.has(requestKey)) {
      return
    }

    const cachedData = dataCache.get(cacheKey)
    const isMonthlyResolution = ResolutionManager.isMonthly(resolution)
    const cacheTimeout = isMonthlyResolution ? (firstDataRequest ? 60 * 1000 : 30 * 1000) : CACHE_DURATION

    const monthlyKey = `${symbol}-1M-first`
    const hasMonthlyCache = dataCache.has(monthlyKey)
    const isFromMonthlyToMinute = hasMonthlyCache && resolution === 1 && firstDataRequest

    // 缓存判断优化：减少缓存复用，确保数据实时性
    const cacheAge = cachedData ? Date.now() - cachedData.timestamp : Infinity
    const shouldForceRefresh = forceRefresh.value ||
      (hasMonthlyCache && !isMonthlyResolution && firstDataRequest) ||
      isFromMonthlyToMinute

    // 对于频繁切换，使用更宽松的缓存策略，确保API调用不被阻止
    const baseCacheTimeout = isMonthlyResolution ? 30 * 1000 : 15 * 1000

    // 只有在缓存很新且不需要强制刷新时才使用缓存
    if (cachedData && cacheAge < baseCacheTimeout && firstDataRequest && !shouldForceRefresh && cachedData.data.length > 0) {
      onHistoryCallback(cachedData.data, { noData: false })
      return
    }


    if (forceRefresh.value) {
      forceRefresh.value = false
    }

    const abortController = new AbortController()
    abortControllers.set(requestKey, abortController)
    pendingRequests.set(requestKey, true)

    try {
      const now = Date.now()
      const requestLimit = isMonthlyResolution && firstDataRequest ?
        Math.max(countBack > 300 ? 300 : countBack, 50) :
        (countBack > 300 ? 300 : countBack)

      const { data } = await getKlinesApi({
        symbol: formatSymbol(symbol),
        market: formatSymbol(symbol).includes('_SWAP') ? 'lpc' : 'spot',
        time_frame: ResolutionManager.getInterval(resolution),
        before: firstDataRequest ? now : preObj.value.time,
        limit: requestLimit,
        origin: 1,
      })

      if (abortController.signal.aborted) {
        return
      }

      if (data) {
        const formattedData = data.e.map(item => {
          const time = Number(item[0])
          const open = Math.abs(Number(item[1]))
          const high = Math.abs(Number(item[2]))
          const low = Math.abs(Number(item[3]))
          const close = Math.abs(Number(item[4]))
          const volume = Math.abs(Number(item[5]))
          return { time, open, high, low, close, volume }
        })

        if (formattedData.length > 0) {
          preObj.value = formattedData[0]
          if (firstDataRequest) {
            dataCache.set(cacheKey, {
              data: formattedData,
              timestamp: now,
              symbol: symbol  // 添加symbol标识用于缓存验证
            })
          }

          // 简化1M处理：统一处理所有周期的数据存储
          if (ResolutionManager.isMonthly(resolution) && firstDataRequest) {
            klineList.value = formattedData
            klineTicker.value = {
              ...formattedData[formattedData.length - 1],
              currentPair: symbol,
              currentPeriod: '1M'
            }
          }
        }

        onHistoryCallback(formattedData, { noData: formattedData.length === 0 })
      } else {
        onErrorCallback('No data received from API')
      }
    } catch (error) {
      if (!abortController.signal.aborted) {
        onErrorCallback(error)
      }
    } finally {
      pendingRequests.delete(requestKey)
      abortControllers.delete(requestKey)
    }
  }
  let lastCompleteBar = ref({})

  let monthlySubscriptionCache = {
    key: null,
    subscription: null,
    pair: null,
    needsUpdate: false
  }

  function handleMonthlyRealtimeUpdate(val1: any, val2: any) {
    if (interval.value !== '1M') return false

    const monthlyKey = `${pair.value}_#_1M`
    const last = (val1[pair.value] || {}).last

    let monthlySubscription = null
    let subscriptionKey = null

    // 优先使用缓存的订阅信息，但需要验证有效性
    if (!monthlySubscriptionCache.needsUpdate &&
      monthlySubscriptionCache.pair === pair.value &&
      monthlySubscriptionCache.key &&
      subMap[monthlySubscriptionCache.key] &&
      subMap[monthlySubscriptionCache.key].symbol === pair.value) {
      monthlySubscription = monthlySubscriptionCache.subscription
      subscriptionKey = monthlySubscriptionCache.key
    } else {
      // 重新查找有效的订阅
      if (subMap[monthlyKey] && subMap[monthlyKey].symbol === pair.value) {
        monthlySubscription = subMap[monthlyKey]
        subscriptionKey = monthlyKey
      } else {
        // 查找所有可能的1M订阅
        Object.keys(subMap).forEach(key => {
          const sub = subMap[key]
          if (sub && typeof sub === 'object' && sub.symbol && 
              formatSymbol(sub.symbol) === pair.value &&
              sub.resolution && ResolutionManager.getInterval(sub.resolution) === '1M') {
            monthlySubscription = sub
            subscriptionKey = key
          }
        })
        
        // 如果还是没找到，可能是订阅键格式不同
        if (!monthlySubscription) {
          const alternativeKey = `${pair.value}_#_1M`
          if (subMap[alternativeKey] && typeof subMap[alternativeKey] === 'object') {
            monthlySubscription = subMap[alternativeKey]
            subscriptionKey = alternativeKey
          }
        }
      }

      // 更新缓存
      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: monthlySubscription,
        pair: pair.value,
        needsUpdate: false
      }
    }

    // 增强数据有效性检查，确保只有在数据完全匹配时才推送
    if (monthlySubscription && last && val2 && val2.currentPair &&
      formatSymbol(monthlySubscription.symbol) === val2.currentPair &&
      val2.currentPeriod === '1M' && val2.time && val2.open !== undefined) {
      // 移除updateTime检查，因为切换回来时可能没有这个字段

      const resultVal = {
        time: Number(val2.time),
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        close: safeNumber(last),
        volume: safeNumber(val2.volume)
      }

      const monthlyStateKey = `${pair.value}_#_1M`
      lastCompleteBar.value[monthlyStateKey] = {
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        volume: safeNumber(val2.volume)
      }

      try {
        monthlySubscription.listen(resultVal)
        return true
      } catch (error) {
        // 如果推送失败，标记需要更新
        monthlySubscriptionCache.needsUpdate = true
        return false
      }
    }

    return false
  }

  watch([ticker, klineTicker], ([val1, val2]) => {
    if (handleMonthlyRealtimeUpdate(val1, val2)) {
      return
    }

    if (interval.value === '1M') {
      return
    }

    const key = `${pair.value}_#_${interval.value}`
    const last = (val1[pair.value] || {}).last

    if (subMap[key] && last && formatSymbol(subMap[key].symbol) === pair.value) {
      let resultVal

      if (val2 && val2.currentPair && val2.currentPeriod &&
        formatSymbol(subMap[key].symbol) === val2.currentPair &&
        interval.value === val2.currentPeriod) {
        resultVal = {
          time: Number(val2.time),
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          close: safeNumber(last),
          volume: safeNumber(val2.volume)
        }
        lastCompleteBar.value[key] = {
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          volume: safeNumber(val2.volume)
        }
      } else if (val2 && val2.currentPair && formatSymbol(subMap[key].symbol) === val2.currentPair) {
        const baseBar = lastCompleteBar.value[key] || {}
        const currentClose = safeNumber(last)
        resultVal = {
          time: Number(val2.time) || Date.now(),
          open: safeNumber(val2.open, baseBar.open || currentClose),
          high: Math.max(safeNumber(val2.high, baseBar.high || currentClose), currentClose),
          low: Math.min(safeNumber(val2.low, baseBar.low || currentClose), currentClose),
          close: currentClose,
          volume: safeNumber(val2.volume, baseBar.volume)
        }
      } else if (lastCompleteBar.value[key]) {
        const baseBar = lastCompleteBar.value[key]
        const currentClose = safeNumber(last)
        resultVal = {
          time: Date.now(),
          open: baseBar.open,
          high: Math.max(baseBar.high, currentClose),
          low: Math.min(baseBar.low, currentClose),
          close: currentClose,
          volume: baseBar.volume
        }
      } else {
        const currentClose = safeNumber(last)
        resultVal = {
          time: Date.now(),
          open: currentClose,
          high: currentClose,
          low: currentClose,
          close: currentClose,
          volume: 0
        }
      }

      // 取消之前的更新请求
      if (rafId) {
        cancelAnimationFrame(rafId)
      }

      // 优化：直接同步推送数据，避免延迟造成的闪烁
      // 双重检查确保订阅仍然有效
      if (subMap[key] && subMap[key].listen && pair.value === formatSymbol(subMap[key].symbol)) {
        try {
          subMap[key].listen(resultVal)
        } catch (error) {
          // 错误回退：使用异步推送
          rafId = requestAnimationFrame(() => {
            if (subMap[key] && subMap[key].listen) {
              try {
                subMap[key].listen(resultVal)
              } catch (retryError) {
                // 忽略重试错误
              }
            }
            rafId = null
          })
        }
      }
    }
  }, { deep: true })

  function subscribeBars(symbolInfo: any, resolution: any, onRealtimeCallback: any, subscriberUID: any, onResetCacheNeededCallback: any) {
    const subscriptionKey = `${symbolInfo.fullName}_#_${ResolutionManager.getInterval(resolution)}`
    const currentKey = `${symbolInfo.fullName}_#_${ResolutionManager.getInterval(resolution)}`


    // 首先清理所有与当前币种不相关的订阅
    Object.keys(subMap).forEach(key => {
      if (typeof subMap[key] === 'object' && subMap[key].symbol && subMap[key].symbol !== symbolInfo.fullName) {
        delete subMap[key]
      }
    })

    // 统一处理订阅，清理旧的避免冲突
    const existingSubscription = subMap[subscriptionKey]
    if (existingSubscription) {
      delete subMap[subscriptionKey]

      // 清理相关的lastCompleteBar
      if (lastCompleteBar.value[currentKey]) {
        delete lastCompleteBar.value[currentKey]
      }

      // 清理月度订阅缓存时保留基本信息，避免完全丢失订阅状态
      if (ResolutionManager.isMonthly(ResolutionManager.getInterval(resolution))) {
        // 不立即清空，而是标记为需要更新
        monthlySubscriptionCache.needsUpdate = true
      }
    }

    // 保存旧订阅信息（用于清理其他不相关订阅）
    const oldSubscriberUID = subMap[subscriberUID]

    // 创建新订阅
    const subscription = {
      resolution,
      symbol: symbolInfo.fullName,
      listen: (newPriceData) => {
        try {
          // 放宽检查条件，确保1M周期切换后能正常推送
          if (!newPriceData) {
            return
          }
          // 对于1M周期，使用更宽松的检查
          if (ResolutionManager.isMonthly(ResolutionManager.getInterval(resolution))) {
            if (formatSymbol(symbolInfo.fullName) === pair.value) {
              onRealtimeCallback(newPriceData)
            }
          } else {
            // 非1M周期保持原有的严格检查
            if (pair.value === symbolInfo.fullName &&
                interval.value === ResolutionManager.getInterval(resolution)) {
              onRealtimeCallback(newPriceData)
            }
          }
        } catch (error) {
          console.error('Realtime callback error:', error)
        }
      }
    }

    // 设置新订阅
    subMap[subscriptionKey] = subscription
    subMap[subscriberUID] = subscriptionKey

    // 更新pair和interval确保数据推送能正确映射
    pair.value = symbolInfo.fullName
    interval.value = ResolutionManager.getInterval(resolution)


    // 强制触发ticker更新，确保watch能正常工作
    if (ticker.value && ticker.value[symbolInfo.fullName]) {
      // 创建ticker的浅拷贝以触发Vue的响应式更新
      const tickerCopy = { ...ticker.value }
      ticker.value = tickerCopy
    }
    
    // 对于1M周期，立即触发一次klineTicker更新以确保订阅生效
    if (ResolutionManager.isMonthly(ResolutionManager.getInterval(resolution)) && 
        klineTicker.value && klineTicker.value.currentPeriod === '1M' &&
        klineTicker.value.currentPair === symbolInfo.fullName) {
      // 触发响应式更新
      klineTicker.value = { ...klineTicker.value }
    }

    // 延迟清理旧订阅，确保新订阅已经建立
    setTimeout(() => {
      if (oldSubscriberUID && typeof oldSubscriberUID === 'string' && oldSubscriberUID !== subscriptionKey) {
        if (subMap[oldSubscriberUID]) {
          delete subMap[oldSubscriberUID]
        }
      }

      // 清理其他不相关的订阅
      Object.keys(subMap).forEach(key => {
        if (key !== subscriptionKey && key !== subscriberUID && subMap[key] !== subscriptionKey) {
          const sub = subMap[key]
          if (sub && typeof sub === 'object' && sub.symbol !== symbolInfo.fullName) {
            delete subMap[key]
          }
        }
      })
    }, 100)

    if (ResolutionManager.isMonthly(ResolutionManager.getInterval(resolution))) {
      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: subscription,
        pair: symbolInfo.fullName,
        needsUpdate: false
      }
    }

    // 初始化lastCompleteBar确保有基础数据结构
    if (!lastCompleteBar.value[currentKey]) {
      lastCompleteBar.value[currentKey] = {}
    }

    // 优化：立即检查并推送当前数据，确保新订阅能无缝衔接
    // 使用更短的延迟确保订阅建立后立即推送数据
    setTimeout(() => {
      if (ticker.value && ticker.value[symbolInfo.fullName]) {
        // 直接触发数据推送，不需要修改store数据
        const subscriptionKey = `${symbolInfo.fullName}_#_${ResolutionManager.getInterval(resolution)}`
        const subscription = subMap[subscriptionKey]

        if (subscription && subscription.listen) {
          const last = ticker.value[symbolInfo.fullName]?.last
          const klineData = klineTicker.value

          // 对于1M周期，使用更宽松的条件
          const isMonthly = ResolutionManager.isMonthly(ResolutionManager.getInterval(resolution))
          const shouldPush = isMonthly ? 
            (last && klineData && klineData.currentPair === symbolInfo.fullName) :
            (last && klineData && klineData.currentPair === symbolInfo.fullName && 
             klineData.currentPeriod === ResolutionManager.getInterval(resolution))

          if (shouldPush) {
            const resultVal = {
              time: klineData.time ? Number(klineData.time) : Date.now(),
              open: klineData.open !== undefined ? safeNumber(klineData.open) : safeNumber(last),
              high: klineData.high !== undefined ? safeNumber(klineData.high) : safeNumber(last),
              low: klineData.low !== undefined ? safeNumber(klineData.low) : safeNumber(last),
              close: safeNumber(last),
              volume: klineData.volume !== undefined ? safeNumber(klineData.volume) : 0
            }

            try {
              subscription.listen(resultVal)

              // 对于1M周期，额外触发klineTicker更新
              if (isMonthly) {
                nextTick(() => {
                  // 触发响应式更新
                  ticker.value = { ...ticker.value }
                  if (klineTicker.value && klineTicker.value.currentPeriod === '1M') {
                    klineTicker.value = { ...klineTicker.value }
                  }
                })
              }
            } catch (error) {
              console.error('Initial push error:', error)
            }
          } else if (last && !klineData) {
            // 如果没有klineData，至少推送一个基本数据
            const basicVal = {
              time: Date.now(),
              open: safeNumber(last),
              high: safeNumber(last),
              low: safeNumber(last),
              close: safeNumber(last),
              volume: 0
            }
            try {
              subscription.listen(basicVal)
            } catch (error) {
              console.error('Basic push error:', error)
            }
          }
        }
      }
    }, 50) // 减少延迟时间，提高响应速度
  }

  return {
    historyCallback: () => { },
    onReady: (cb: any) => {
      const config = {
        supported_resolutions: [
          '1',
          '3',
          '5',
          '15',
          '30',
          '60',
          '120',
          '240',
          '360',
          '480',
          '720',
          '1D',
          '3D',
          '1W',
          '1M'
        ]
      }
      const timer = setTimeout(() => {
        cb(config)
        clearTimeout(timer)
      }, 0)
    },
    resolveSymbol(symbolName: string, onSymbolResolveCallback: any) {
      let pricescaleValue = pairInfo[symbolName]?.price_scale || 8
      pricescaleValue = pricescaleValue > 16 ? 16 : pricescaleValue
      const symbolInfo = {
        symbol: symbolName.includes('SWAP') ? symbolName.replace('_SWAP', '').replace('_', '/') : symbolName.replace('_', '/'),
        name: symbolName,
        ticker: symbolName,
        fullName: symbolName,
        discription: '',
        exchange: 'KTX',
        type: 'Spot',
        has_intraday: true,
        minmov: 1,
        minmove2: 0,
        pricescale: Math.pow(10, pricescaleValue),
        timezone: ' ',
        session: '0000-2400:2345671;1',
        volume_precision: 2,
        has_weekly_and_monthly: true,
        has_empty_bars: true
      }
      const timer = setTimeout(() => {
        onSymbolResolveCallback(symbolInfo)
        clearTimeout(timer)
      }, 0)
    },
    getBars,
    subscribeBars,
    unsubscribeBars(subscriberUID: string) {
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }
      const subscriptionKey = subMap[subscriberUID]

      if (subscriptionKey && typeof subscriptionKey === 'string') {
        const subscription = subMap[subscriptionKey]
        if (subscription) {

          if (subscription.resolution && ResolutionManager.getInterval(subscription.resolution) === '1M') {
            monthlySubscriptionCache = {
              key: null,
              subscription: null,
              pair: null
            }
          }

          // 清理相关的lastCompleteBar
          const currentKey = `${subscription.symbol}_#_${ResolutionManager.getInterval(subscription.resolution)}`
          if (lastCompleteBar.value[currentKey]) {
            delete lastCompleteBar.value[currentKey]
          }
        }
        delete subMap[subscriptionKey]
      }
      delete subMap[subscriberUID]

    },
    clearCache,
    setForceRefresh,
    checkCacheValid,
    clearCacheWithCheck,
    preloadData,
    // 调试辅助函数
    getSubscriptionStatus: () => ({
      subMap: Object.keys(subMap).reduce((acc, key) => {
        const sub = subMap[key]
        acc[key] = typeof sub === 'string' ? sub : (sub ? { symbol: sub.symbol, resolution: sub.resolution } : null)
        return acc
      }, {}),
      pair: pair.value,
      interval: interval.value,
      lastCompleteBar: lastCompleteBar.value,
      monthlyCache: monthlySubscriptionCache,
      activeBarsRequests: Array.from(activeBarsRequests.keys()),
      pendingRequests: Array.from(pendingRequests.keys()),
      abortControllers: Array.from(abortControllers.keys()),
      cacheSize: dataCache.size
    }),

    // 强制重置状态的调试函数
    forceReset: () => {
      activeBarsRequests.clear()
      pendingRequests.clear()
      abortControllers.forEach(controller => controller.abort())
      abortControllers.clear()
      dataCache.clear()
      Object.keys(subMap).forEach(key => delete subMap[key])
      lastCompleteBar.value = {}
      monthlySubscriptionCache = { key: null, subscription: null, pair: null, needsUpdate: false }
      forceRefresh.value = true
    },

    // 手动触发数据更新
    manualTriggerUpdate: () => {
      const currentKey = `${pair.value}_#_${interval.value}`
      const subscription = subMap[currentKey]

      if (subscription && ticker.value[pair.value]) {
        const last = ticker.value[pair.value].last
        if (last) {
          const klineData = klineTicker.value
          const resultVal = {
            time: klineData && klineData.time ? Number(klineData.time) : Date.now(),
            open: klineData && klineData.open !== undefined ? safeNumber(klineData.open) : safeNumber(last),
            high: klineData && klineData.high !== undefined ? safeNumber(klineData.high) : safeNumber(last),
            low: klineData && klineData.low !== undefined ? safeNumber(klineData.low) : safeNumber(last),
            close: safeNumber(last),
            volume: klineData && klineData.volume !== undefined ? safeNumber(klineData.volume) : 0
          }
          subscription.listen(resultVal)
          // 同时触发ticker更新
          ticker.value = { ...ticker.value }
        }
      }
    }
  }
}