import { storeToRefs } from "pinia"
import { ref, watch, nextTick } from "vue"
import { commonStore } from "~/stores/commonStore"
import { getKlinesApi } from '~/api/order'

export class ResolutionManager {
  static readonly RESOLUTION_MAP: any = {
    1: '1m',
    5: '5m',
    15: '15m',
    30: '30m',
    60: '1h',
    120: '2h',
    240: '4h',
    360: '6h',
    480: '8h',
    720: '12h',
    '1D': '1d',
    '1W': '1w',
    '1M': '1M'
  }

  static readonly RESOLUTION_RE_MAP: any = {
    'line': 1,
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '2h': 120,
    '4h': 240,
    '6h': 360,
    '8h': 480,
    '12h': 720,
    '1d': '1D',
    '1w': '1W',
    '1M': '1M'
  }

  static isMonthly(resolution: string): boolean {
    return resolution === '1M'
  }

  static getInterval(resolution: string): string {
    return this.RESOLUTION_MAP[resolution] || resolution
  }

  static getResolution(interval: string): string {
    return this.RESOLUTION_RE_MAP[interval] || interval
  }
}

interface DataParams {
  symbol: string
  resolution: string
  from: number
  to: number
  firstDataRequest: boolean
  countBack: number
}

interface DataResult {
  data: any[]
  noData: boolean
}

abstract class DataStrategy {
  protected dataCache: Map<string, any>
  protected CACHE_DURATION: number

  constructor(dataCache: Map<string, any>, cacheDuration: number) {
    this.dataCache = dataCache
    this.CACHE_DURATION = cacheDuration
  }

  abstract async getData(params: DataParams, onHistoryCallback: any, onErrorCallback: any): Promise<boolean>
  abstract getCacheKey(params: DataParams): string
  abstract getCacheTimeout(): number
}

class StandardDataStrategy extends DataStrategy {
  getCacheKey(params: DataParams): string {
    return `${params.symbol}-${params.resolution}-${params.firstDataRequest ? 'first' : params.from}`
  }

  getCacheTimeout(): number {
    return this.CACHE_DURATION
  }

  async getData(params: DataParams, onHistoryCallback: any, onErrorCallback: any): Promise<boolean> {
    return false
  }
}

class MonthlyDataStrategy extends DataStrategy {
  private klineList: any
  private klineTicker: any
  private preObj: any

  constructor(dataCache: Map<string, any>, cacheDuration: number, klineList: any, klineTicker: any, preObj: any) {
    super(dataCache, cacheDuration)
    this.klineList = klineList
    this.klineTicker = klineTicker
    this.preObj = preObj
  }

  getCacheKey(params: DataParams): string {
    return `${params.symbol}-1M-first`
  }

  getCacheTimeout(): number {
    return 60 * 1000
  }

  async getData(params: DataParams, onHistoryCallback: any, onErrorCallback: any): Promise<boolean> {
    if (!params.firstDataRequest || !ResolutionManager.isMonthly(params.resolution)) {
      return false
    }

    // 首先检查store中是否有当前币种的1M数据
    if (this.klineList.value.length && this.klineTicker.value.currentPeriod === '1M' &&
        this.klineTicker.value.currentPair === params.symbol) {
      this.preObj.value = this.klineList.value[0]
      onHistoryCallback(this.klineList.value, {noData: this.klineList.value.length === 0})
      return true
    }

    // 检查缓存
    const cacheKey = this.getCacheKey(params)
    const cachedData = this.dataCache.get(cacheKey)
    
    if (cachedData && (Date.now() - cachedData.timestamp < 60 * 1000)) {
      this.klineList.value = cachedData.data
      if (cachedData.data.length > 0) {
        this.klineTicker.value = {
          ...cachedData.data[cachedData.data.length - 1],
          currentPair: params.symbol,
          currentPeriod: '1M'
        }
        this.preObj.value = cachedData.data[0]
      }
      onHistoryCallback(cachedData.data, {noData: cachedData.data.length === 0})
      return true
    }

    // 如果没有store数据也没有缓存，返回false让常规逻辑处理
    return false
  }

}

class DataStrategyFactory {
  static createStrategy(resolution: string, dataCache: Map<string, any>, cacheDuration: number, klineList?: any, klineTicker?: any, preObj?: any): DataStrategy {
    if (ResolutionManager.isMonthly(resolution)) {
      return new MonthlyDataStrategy(dataCache, cacheDuration, klineList, klineTicker, preObj)
    }
    return new StandardDataStrategy(dataCache, cacheDuration)
  }
}

export default function useDatafeedAction(info) {
  const dataCache = new Map()
  const CACHE_DURATION = 2 * 60 * 1000
  const pairInfo = info
  const store = commonStore()
  const interval = ref('')
  const pair = ref('')
  const preObj = ref({})
  const { klineList, klineTicker, ticker } = storeToRefs(store)
  
  let debounceTimer = null
  const pendingRequests = new Map()
  const abortControllers = new Map()
  const activeBarsRequests = new Map()
  const subMap: any = {}
  let rafId: number | null = null

  function formatSymbol (symbol: string) {
    return symbol.toUpperCase()
  }

  const safeNumber = (value: any, fallback: number = 0): number => {
    const num = Number(value)
    return isNaN(num) || !isFinite(num) ? fallback : Math.abs(num)
  }
  let key = ''
  async function handleMonthlyData(symbolInfo: any, resolution: any, periodParams: any, onHistoryCallback: any, onErrorCallback: any) {
    const strategy = DataStrategyFactory.createStrategy(resolution, dataCache, CACHE_DURATION, klineList, klineTicker, preObj)
    
    const params: DataParams = {
      symbol: symbolInfo.fullName,
      resolution,
      from: periodParams.from,
      to: periodParams.to,
      firstDataRequest: periodParams.firstDataRequest,
      countBack: periodParams.countBack
    }
    
    return await strategy.getData(params, onHistoryCallback, onErrorCallback)
  }

  async function getBars(
    symbolInfo: any,
    resolution: any,
    periodParams: any,
    onHistoryCallback: any,
    onErrorCallback: any) {
    console.log(symbolInfo, 'symbolInfoGetBars', resolution, periodParams.firstDataRequest)
    const currentSymbol = symbolInfo.fullName
    const { from, to, firstDataRequest, countBack } = periodParams;
    
    // 创建请求唯一标识
    const requestId = `${currentSymbol}-${resolution}-${firstDataRequest ? 'first' : 'history'}`
    
    // 如果相同的请求正在进行，直接返回
    if (activeBarsRequests.has(requestId)) {
      console.log('相同请求正在进行，跳过:', requestId)
      return
    }
    
    // 标记请求开始
    activeBarsRequests.set(requestId, true)
    
    pair.value = currentSymbol
    interval.value = ResolutionManager.getInterval(resolution)
    key = `${currentSymbol}-${resolution}`

    // 取消之前的防抖定时器
    if (debounceTimer) {
      clearTimeout(debounceTimer)
      debounceTimer = null
    }

    // 保守的防抖优化：轻微减少延迟但保持稳定性
    const debounceDelay = firstDataRequest ? 
      (ResolutionManager.isMonthly(resolution) ? 40 : 30) :  // 轻微减少首次请求延迟
      (ResolutionManager.isMonthly(resolution) ? 100 : 80)   // 保持相对保守的历史请求延迟

    const executeRequest = async () => {
      try {
        if (resolution === '1M') {
          const handled = await handleMonthlyData(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback)
          if (handled) {
            return
          }
        }

        await fetchHistoricalData(symbolInfo.fullName, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback);
      } finally {
        activeBarsRequests.delete(requestId)
      }
    }

    // 所有请求都使用防抖
    debounceTimer = setTimeout(executeRequest, debounceDelay)
  }
  const forceRefresh = ref(false)

  const clearCache = () => {
    dataCache.clear()
    lastCompleteBar.value = {}
    monthlySubscriptionCache = {
      key: null,
      subscription: null,
      pair: null
    }
    klineList.value = []
    klineTicker.value = {}
    preObj.value = {}

    Object.keys(subMap).forEach(key => {
      delete subMap[key]
    })

    if (rafId) {
      cancelAnimationFrame(rafId)
      rafId = null
    }

    // 清理防抖定时器和请求
    if (debounceTimer) {
      clearTimeout(debounceTimer)
      debounceTimer = null
    }
    
    // 取消所有pending的请求
    abortControllers.forEach(controller => {
      controller.abort()
    })
    abortControllers.clear()
    pendingRequests.clear()
    activeBarsRequests.clear()
  }

  const setForceRefresh = (force: boolean) => {
    forceRefresh.value = force
    if (force) {
      clearCache()
    }
  }

  // 新增：检查缓存有效性的辅助方法（保守策略）
  const checkCacheValid = (symbol: string, resolution: string): boolean => {
    const cacheKey = `${symbol}-${resolution}-first`
    const cachedData = dataCache.get(cacheKey)
    
    if (!cachedData) return false
    
    const now = Date.now()
    const isMonthly = ResolutionManager.isMonthly(resolution)
    const cacheTimeout = isMonthly ? 60 * 1000 : CACHE_DURATION
    
    // 只有缓存很新才返回true（比原来更保守，只在30%的缓存时间内或最多10秒）
    const isVeryFresh = (now - cachedData.timestamp) < Math.min(cacheTimeout * 0.3, 10000)
    
    return isVeryFresh && cachedData.data && cachedData.data.length > 0
  }

  // 新增：带检查的缓存清理方法（保守策略）
  const clearCacheWithCheck = (symbol?: string, resolution?: string): boolean => {
    if (symbol && resolution) {
      const cacheKey = `${symbol}-${resolution}-first`
      const cachedData = dataCache.get(cacheKey)
      
      // 如果缓存很新（5秒内），跳过这次清理
      if (cachedData && (Date.now() - cachedData.timestamp < 5000)) {
        console.log('跳过清理新鲜缓存:', cacheKey)
        return false // 表示没有清理
      }
    }
    
    // 执行原有的完整清理逻辑
    clearCache()
    return true // 表示执行了清理
  }
  async function fetchHistoricalData(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any) {
    // 新增：快速缓存检查（更保守的条件）
    if (firstDataRequest) {
      const quickCacheKey = `${symbol}-${resolution}-first`
      const cachedData = dataCache.get(quickCacheKey)
      
      if (cachedData) {
        const now = Date.now()
        const isMonthly = ResolutionManager.isMonthly(resolution)
        const quickCacheTimeout = isMonthly ? 30 * 1000 : 60 * 1000 // 更短的快速缓存时间
        
        // 只有缓存非常新鲜且数据完整才直接返回
        if ((now - cachedData.timestamp < quickCacheTimeout) && 
            cachedData.data && 
            cachedData.data.length > 20) { // 确保数据完整性
          console.log('使用快速缓存:', quickCacheKey)
          onHistoryCallback(cachedData.data, { noData: false })
          return
        }
      }
    }

    // 保持完全原有的逻辑，包括所有现有的缓存检查
    const cacheKey = `${symbol}-${resolution}-${firstDataRequest ? 'first' : from}`
    const requestKey = `${symbol}-${resolution}-${from}-${to}-${firstDataRequest}`
    
    // 检查是否有相同的请求正在进行
    if (pendingRequests.has(requestKey)) {
      return
    }
    
    const cachedData = dataCache.get(cacheKey)
    // 1M周期需要较短的缓存时间以保证数据新鲜度
    const isMonthlyResolution = ResolutionManager.isMonthly(resolution)
    const cacheTimeout = isMonthlyResolution ? 60 * 1000 : CACHE_DURATION

    const monthlyKey = `${symbol}-1M-first`
    const hasMonthlyCache = dataCache.has(monthlyKey)
    const isFromMonthlyToMinute = hasMonthlyCache && resolution === 1 && firstDataRequest
    const shouldForceRefresh = forceRefresh.value || (hasMonthlyCache && !isMonthlyResolution && firstDataRequest) || isFromMonthlyToMinute

    if (cachedData && (Date.now() - cachedData.timestamp < cacheTimeout) && firstDataRequest && !shouldForceRefresh) {
      onHistoryCallback(cachedData.data, { noData: cachedData.data.length === 0 })
      return
    }

    if (forceRefresh.value) {
      forceRefresh.value = false
    }

    // 创建 AbortController 用于取消请求
    const abortController = new AbortController()
    abortControllers.set(requestKey, abortController)
    pendingRequests.set(requestKey, true)

    try {
      const now = Date.now()
      // 简化请求限制逻辑：统一处理所有周期
      const requestLimit = countBack > 300 ? 300 : Math.max(countBack, 50)

      const { data } = await getKlinesApi({
        symbol: formatSymbol(symbol),
        market: formatSymbol(symbol).includes('_SWAP') ? 'lpc' : 'spot',
        time_frame: ResolutionManager.getInterval(resolution),
        before: firstDataRequest ? now : preObj.value.time,
        limit: requestLimit,
        origin:1,
      })

      // 检查请求是否被取消
      if (abortController.signal.aborted) {
        return
      }

      if (data) {
        const formattedData = data.e.map(item => {
          const time = Number(item[0])
          const open = Math.abs(Number(item[1]))
          const high = Math.abs(Number(item[2]))
          const low = Math.abs(Number(item[3]))
          const close = Math.abs(Number(item[4]))
          const volume = Math.abs(Number(item[5]))
          return { time, open, high, low, close, volume }
        })

        if (formattedData.length > 0) {
          preObj.value = formattedData[0]
          if (firstDataRequest) {
            dataCache.set(cacheKey, {
              data: formattedData,
              timestamp: now
            })
          }

          // 简化1M处理：统一处理所有周期的数据存储
          if (ResolutionManager.isMonthly(resolution) && firstDataRequest) {
            klineList.value = formattedData
            klineTicker.value = {
              ...formattedData[formattedData.length - 1],
              currentPair: symbol,
              currentPeriod: '1M'
            }
          }
        }

        onHistoryCallback(formattedData, { noData: formattedData.length === 0 })
      } else {
        onErrorCallback('No data received from API')
      }
    } catch (error) {
      if (!abortController.signal.aborted) {
        onErrorCallback(error)
      }
    } finally {
      // 清理请求记录
      pendingRequests.delete(requestKey)
      abortControllers.delete(requestKey)
    }
  }
  let lastCompleteBar = ref({})

  let monthlySubscriptionCache = {
    key: null,
    subscription: null,
    pair: null
  }

  function handleMonthlyRealtimeUpdate(val1: any, val2: any) {
    if (interval.value !== '1M') return false

    const monthlyKey = `${pair.value}_#_1M`
    const last = (val1[pair.value] || {}).last

    let monthlySubscription = null
    let subscriptionKey = null

    if (monthlySubscriptionCache.pair === pair.value &&
        monthlySubscriptionCache.key &&
        subMap[monthlySubscriptionCache.key]) {
      monthlySubscription = monthlySubscriptionCache.subscription
      subscriptionKey = monthlySubscriptionCache.key
    } else {
      if (subMap[monthlyKey]) {
        monthlySubscription = subMap[monthlyKey]
        subscriptionKey = monthlyKey
      } else {
        Object.keys(subMap).forEach(key => {
          const sub = subMap[key]
          if (sub && sub.symbol && formatSymbol(sub.symbol) === pair.value &&
              sub.resolution && ResolutionManager.getInterval(sub.resolution) === '1M') {
            monthlySubscription = sub
            subscriptionKey = key
          }
        })
      }

      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: monthlySubscription,
        pair: pair.value
      }
    }

    if (monthlySubscription && last && val2 && val2.currentPair &&
        formatSymbol(monthlySubscription.symbol) === val2.currentPair &&
        val2.currentPeriod === '1M' && val2.time && val2.open !== undefined) {

      const resultVal = {
        time: Number(val2.time),
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        close: safeNumber(last),
        volume: safeNumber(val2.volume)
      }

      const monthlyStateKey = `${pair.value}_#_1M`
      lastCompleteBar.value[monthlyStateKey] = {
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        volume: safeNumber(val2.volume)
      }

      monthlySubscription.listen(resultVal)
      return true
    }

    return false
  }

  watch([ticker, klineTicker], ([val1, val2]) => {
    if (handleMonthlyRealtimeUpdate(val1, val2)) {
      return
    }

    if (interval.value === '1M') {
      return
    }

    const key = `${pair.value}_#_${interval.value}`
    const last = (val1[pair.value] || {}).last

    if (subMap[key] && last && formatSymbol(subMap[key].symbol) === pair.value) {
      let resultVal

      if (val2 && val2.currentPair && val2.currentPeriod &&
          formatSymbol(subMap[key].symbol) === val2.currentPair &&
          interval.value === val2.currentPeriod) {
        resultVal = {
          time: Number(val2.time),
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          close: safeNumber(last),
          volume: safeNumber(val2.volume)
        }
        lastCompleteBar.value[key] = {
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          volume: safeNumber(val2.volume)
        }
      } else if (val2 && val2.currentPair && formatSymbol(subMap[key].symbol) === val2.currentPair) {
        const baseBar = lastCompleteBar.value[key] || {}
        const currentClose = safeNumber(last)
        resultVal = {
          time: Number(val2.time) || Date.now(),
          open: safeNumber(val2.open, baseBar.open || currentClose),
          high: Math.max(safeNumber(val2.high, baseBar.high || currentClose), currentClose),
          low: Math.min(safeNumber(val2.low, baseBar.low || currentClose), currentClose),
          close: currentClose,
          volume: safeNumber(val2.volume, baseBar.volume)
        }
      } else if (lastCompleteBar.value[key]) {
        const baseBar = lastCompleteBar.value[key]
        const currentClose = safeNumber(last)
        resultVal = {
          time: Date.now(),
          open: baseBar.open,
          high: Math.max(baseBar.high, currentClose),
          low: Math.min(baseBar.low, currentClose),
          close: currentClose,
          volume: baseBar.volume
        }
      } else {
        const currentClose = safeNumber(last)
        resultVal = {
          time: Date.now(),
          open: currentClose,
          high: currentClose,
          low: currentClose,
          close: currentClose,
          volume: 0
        }
      }

      if (rafId) {
        cancelAnimationFrame(rafId)
      }

      rafId = requestAnimationFrame(() => {
        if (subMap[key] && subMap[key].listen) {
          subMap[key].listen(resultVal)
        }
        rafId = null
      })
    }
  }, { deep: true })

  function subscribeBars(symbolInfo: any, resolution: any, onRealtimeCallback: any, subscriberUID: any, onResetCacheNeededCallback: any) {
    const subscriptionKey = `${symbolInfo.fullName}_#_${ResolutionManager.getInterval(resolution)}`

    if (subMap[subscriptionKey]) {
      delete subMap[subscriptionKey]
    }
    if (subMap[subscriberUID]) {
      const oldKey = subMap[subscriberUID]
      if (typeof oldKey === 'string' && subMap[oldKey]) {
        delete subMap[oldKey]
      }
      delete subMap[subscriberUID]
    }

    if (ResolutionManager.isMonthly(ResolutionManager.getInterval(resolution))) {
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
    }

    const currentKey = `${symbolInfo.fullName}_#_${ResolutionManager.getInterval(resolution)}`
    Object.keys(lastCompleteBar.value).forEach(key => {
      if (key !== currentKey) {
        delete lastCompleteBar.value[key]
      }
    })

    const subscription = {
      resolution,
      symbol: symbolInfo.fullName,
      listen: (newPriceData) => {
        try {
          if (pair.value !== symbolInfo.fullName || !newPriceData) {
            return
          }
          onRealtimeCallback(newPriceData)
        } catch (error) {
          console.error('Error in realtime callback:', error)
        }
      }
    }

    subMap[subscriptionKey] = subscription
    subMap[subscriberUID] = subscriptionKey

    if (ResolutionManager.isMonthly(ResolutionManager.getInterval(resolution))) {
      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: subscription,
        pair: symbolInfo.fullName
      }
    }
  }

  return {
    historyCallback: () => {},
    onReady: (cb: any) => {
      const config = {
        supported_resolutions: [
          '1',
          '3',
          '5',
          '15',
          '30',
          '60',
          '120',
          '240',
          '360',
          '480',
          '720',
          '1D',
          '3D',
          '1W',
          '1M'
        ]
      }
      const timer = setTimeout(() => {
        cb(config)
        clearTimeout(timer)
      }, 0)
    },
    resolveSymbol(symbolName: string, onSymbolResolveCallback: any) {
      let pricescaleValue = pairInfo[symbolName]?.price_scale || 8
      pricescaleValue = pricescaleValue > 16 ? 16 : pricescaleValue
      const symbolInfo = {
        symbol: symbolName.includes('SWAP') ? symbolName.replace('_SWAP', '').replace('_', '/') : symbolName.replace('_', '/'),
        name: symbolName,
        ticker: symbolName,
        fullName: symbolName,
        discription: '',
        exchange: 'KTX',
        type: 'Spot',
        has_intraday: true,
        minmov: 1,
        minmove2: 0,
        pricescale: Math.pow(10, pricescaleValue),
        timezone: ' ', // 时区
        session: '0000-2400:2345671;1',
        volume_precision: 2,
        has_weekly_and_monthly: true,
        has_empty_bars: true
      }
      const timer = setTimeout(() => {
        onSymbolResolveCallback(symbolInfo)
        clearTimeout(timer)
      }, 0)
    },
    getBars,
    subscribeBars,
    unsubscribeBars(subscriberUID: string) {
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }

      const subscriptionKey = subMap[subscriberUID]

      if (subscriptionKey && typeof subscriptionKey === 'string') {
        const subscription = subMap[subscriptionKey]
        if (subscription && subscription.resolution && ResolutionManager.getInterval(subscription.resolution) === '1M') {
          monthlySubscriptionCache = {
            key: null,
            subscription: null,
            pair: null
          }
        }
        delete subMap[subscriptionKey]
      }
      delete subMap[subscriberUID]
    },
    clearCache,
    setForceRefresh,
    // 新增的辅助方法
    checkCacheValid,
    clearCacheWithCheck
  }
}